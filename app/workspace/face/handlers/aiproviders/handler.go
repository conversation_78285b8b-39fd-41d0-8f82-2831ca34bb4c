package aiproviders

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"sa/pkg/logging"
)

type Handler struct {
	Logger logging.Logger
}

func (h *Handler) Register(rg *gin.RouterGroup) {
	rg.GET("", h.AIProviders)
}

// AIProviders returns list of available AI providers
// @Summary List all AI providers
// @Tags AIProviders
// @Security BasicAuth
// @Accept json
// @Produce json
// @Success 200 {array} RspAIProviders
// @Router /aiproviders [get]
func (h *Handler) AIProviders(ctx *gin.Context) {
	rsp := buildAIProvidersList()
	ctx.JSON(http.StatusOK, rsp)
}

func buildAIProvidersList() RspAIProviders {
	providers := RspAIProviders{}

	openAIModels := []OpenAIModel{
		OpenAIGPT4, OpenAIGPT4Turbo, OpenAIGPT41, OpenAIGPT41Mini, OpenAIGPT41Nano,
		OpenAIGPT4O, OpenAIGPT45, OpenAIGPT35Turbo, OpenAIO1, OpenAIO1Mini,
		OpenAIO1Pro, OpenAIO3, OpenAIO3Mini, OpenAIO4Mini,
	}
	for _, model := range openAIModels {
		providers = append(providers, RspAIProviderItem{
			Vendor: "openai",
			Model:  model,
		})
	}

	azureDeployments := []AzureOpenAIDeployment{
		{DeploymentName: "gpt-41-deployment", ModelID: AzureOpenAIGPT41},
		{DeploymentName: "gpt-41-mini-deployment", ModelID: AzureOpenAIGPT41Mini},
		{DeploymentName: "gpt-41-nano-deployment", ModelID: AzureOpenAIGPT41Nano},
		{DeploymentName: "gpt-35-turbo-deployment", ModelID: AzureOpenAIGPT35Turbo},
	}
	for _, deployment := range azureDeployments {
		providers = append(providers, RspAIProviderItem{
			Vendor:     "azure-openai",
			Deployment: &deployment,
		})
	}

	claudeModels := []ClaudeModel{
		Claude3Haiku, Claude3Sonnet, Claude3Opus, Claude35Haiku,
		Claude35Sonnet, Claude37Sonnet, Claude4Opus, Claude4Sonnet,
	}
	for _, model := range claudeModels {
		providers = append(providers, RspAIProviderItem{
			Vendor: "claude",
			Model:  model,
		})
	}

	deepSeekModels := []DeepSeekModel{
		DeepSeekR1, DeepSeekV2, DeepSeekV3, DeepSeekCoder, DeepSeekCoderV2, DeepSeekMath,
	}
	for _, model := range deepSeekModels {
		providers = append(providers, RspAIProviderItem{
			Vendor: "deepseek",
			Model:  model,
		})
	}

	return providers
}
