package workspace

import "time"

type Req struct {
	Name  string  `json:"name"  default:"My workspace"`
	Photo *string `json:"photo,omitempty" default:"https://account.corezoid.com/avatars/0.jpg"`
	Color *string `json:"color,omitempty" default:"#000000"`
}
type AISetting struct {
	Provider string `json:"provider,omitempty"`
	Model    string `json:"model,omitempty"`
	APIKey   string `json:"api_key,omitempty"`
}

type AISettings struct {
	Actor   AISetting   `json:"actor,omitempty"`
	System  AISetting   `json:"system,omitempty"`
	Console []AISetting `json:"console,omitempty"`
}

type Settings struct {
	DisableInvites     *bool      `json:"disable_invites,omitempty" default:"false"`
	AllowedDomains     *[]string  `json:"allowed_domains,omitempty"`
	ForbiddenDomainsRe *[]string  `json:"forbidden_domains,omitempty"`
	SimClient          *string    `json:"sim_client,omitempty"`
	AutoRecording      *bool      `json:"auto_recording,omitempty" default:"false"`
	TranscriptionLang  *string    `json:"transcription_lang,omitempty" default:"en"`
	FileTTL            *int       `json:"file_ttl,omitempty"`
	AISettings         AISettings `json:"ai_settings,omitempty"`
}

type ReqUpdate struct {
	Photo *string `json:"photo,omitempty" default:"https://account.corezoid.com/avatars/0.jpg"`
	Color *string `json:"color,omitempty" default:"#000000"`
	Name  string  `json:"name,omitempty" default:"My workspace"`
	// Workspace status, available values: active/block
	Status   string   `json:"status,omitempty" default:"active"`
	Settings Settings `json:"settings"`
}

type RoleRow struct {
	ID   int    `json:"id" default:"1"`
	Name string `json:"name" default:"Member"`
}

type RspItem struct {
	ID         int        `json:"id" default:"1"`
	ExtID      string     `json:"ext_id" default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
	Name       string     `json:"name" default:"My workspace"`
	Photo      *string    `json:"photo,omitempty"`
	Color      *string    `json:"color,omitempty"`
	Status     string     `json:"status" default:"active"`
	UserStatus string     `json:"user_status" default:"active" enum:"active,blocked"`
	CreatedAt  time.Time  `json:"created_at"  default:"2022-10-10 09-09-09"`
	Owners     []OwnerRow `json:"owners"`
	Roles      []RoleRow  `json:"roles"`
}

type ShortRsp struct {
	Meta RspMeta   `json:"meta"`
	Data []RspItem `json:"data"`
}
type RspMeta struct {
	Total int `json:"total"`
}

type LicenseInfoRsp struct {
	ID          int    `json:"id" default:"1"`
	PaymentType string `json:"payment_type" default:"free"`
}

type Rsp struct {
	ID          int        `json:"id" default:"1"`
	ExtID       string     `json:"ext_id" default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
	Name        string     `json:"name" default:"My workspace"`
	Photo       *string    `json:"photo,omitempty"`
	Color       *string    `json:"color,omitempty"`
	Status      string     `json:"status" default:"active"`
	CreatedAt   time.Time  `json:"created_at"  default:"2022-10-10 09-09-09"`
	UserCount   int        `json:"user_count" default:"1"`
	InviteCount int        `json:"invite_count" default:"1"`
	GroupCount  int        `json:"group_count" default:"1"`
	RoleCount   int        `json:"role_count" default:"1"`
	APICount    int        `json:"api_count" default:"1"`
	Type        string     `json:"type" default:"null" enum:"null,regular"`
	Owners      []OwnerRow `json:"owners"`
	Settings    Settings   `json:"settings"`
}

type CreateRsp struct {
	ExtID string `json:"ext_id"  default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
}

type OwnerRow struct {
	ID    int     `json:"id" default:"1"`
	Name  string  `json:"name" default:"Ivan Ivanov"`
	Photo *string `json:"photo,omitempty" default:"https://account.corezoid.com/avatars/0.jpg"`
	Color *string `json:"color,omitempty" default:"#000000"`
}
