package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"log/slog"
	"net/http"
	"net/url"
	"sa/app/auth/handlers/providers"
	"sa/apperr"
	"sa/config"
	"sa/internal/authz"
	"sa/internal/cache"
	"sa/internal/captcha"
	"sa/internal/client"
	"sa/internal/event"
	"sa/internal/group"
	"sa/internal/invite"
	"sa/internal/license"
	"sa/internal/mfa"
	"sa/internal/organization"
	"sa/internal/role"
	"sa/internal/user"
	"sa/internal/webhook"
	"sa/internal/workspace"
	"sa/pkg/httputil"
	"sa/pkg/jsonschema"
	"sa/pkg/logging"
	"sa/pkg/ptrutil"
	"sa/pkg/rand"
	"strconv"
	"strings"
	"time"

	"github.com/MicahParks/keyfunc"
	"github.com/cristalhq/jwt/v3"
	"github.com/gin-gonic/gin"
	jwt1 "github.com/golang-jwt/jwt/v4"
	"github.com/markbates/goth/gothic"
)

type ErrRsp struct {
	RequestID string `json:"request_id"`
	Result    string `json:"result"`
	Message   string `json:"message"`
}

type AuthHandler struct {
	apiValidator  jsonschema.APIValidator
	JWKS          *keyfunc.JWKS
	Logger        logging.Logger
	Cfg           *config.Config
	US            *user.Service
	RS            *role.Service
	GS            group.Service
	IS            invite.Service
	WS            *workspace.Service
	OS            *organization.Service
	LS            *license.Service
	MFA           *mfa.Service
	Captcha       *captcha.Service
	CS            client.Service
	ldapProvider  *providers.LDAPProvider
	gothProviders map[string]providers.Provider
	samlProviders map[string]*providers.SAMLProvider
}

func (h *AuthHandler) Register(ctx context.Context, rg *gin.RouterGroup) {

	store := &providers.Store{CS: h.US.Cache}
	h.gothProviders = make(map[string]providers.Provider)
	h.samlProviders = make(map[string]*providers.SAMLProvider)
	gothic.Store = store

	if h.Cfg.AuthProviders.OpenidConnect != nil {
		for name, data := range h.Cfg.AuthProviders.OpenidConnect {
			if !data.Enabled {
				continue
			}
			var p providers.Provider
			if name == "apple" {
				p = providers.NewAppleProvider(data, h.US)
			} else {
				p = providers.NewOpenIDConnectProvider(ctx, name, data, h.US)
			}
			h.gothProviders[name] = p
		}
	}
	if h.Cfg.AuthProviders.Oauth2 != nil {
		for name, data := range h.Cfg.AuthProviders.Oauth2 {
			if !data.Enabled {
				continue
			}
			p := providers.NewOauth2Provider(name, data, h.US)
			h.gothProviders[name] = p
		}
	}
	if h.Cfg.AuthProviders.OauthPB != nil && h.Cfg.AuthProviders.OauthPB.Enabled {
		p := providers.NewAuthPBProvider(h.Cfg.AuthProviders.OauthPB, h.US)
		h.gothProviders["oauth_pb"] = p
	}
	if h.Cfg.AuthProviders.SAML != nil {
		for name, data := range h.Cfg.AuthProviders.SAML {
			if !data.Enabled {
				continue
			}
			p, err := providers.NewSAMLProvider(ctx, rg, name, data, h.US, false)
			apperr.Error(err, "failed to init SAMLProvider")
			h.samlProviders[name] = p
		}
	}
	if h.Cfg.AuthProviders.LDAP != nil && h.Cfg.AuthProviders.LDAP.Enabled {
		p, err := providers.NewLDAPProvider(ctx, h.US, h.Cfg, h.Cfg.AuthProviders.LDAP)
		apperr.Error(err, "failed to init LDAPProvider")
		h.ldapProvider = p

	}
	if h.US.Cfg.Keycloak.Sync {
		h.InitKeycloak(ctx)
	}

	rg.GET("/", func(c *gin.Context) {
		htmlFormat := `<html><body>%v</body></html>`
		html := fmt.Sprintf(htmlFormat, "Test")
		c.Data(http.StatusOK, "text/html; charset=utf-8", []byte(html))
	})
	rg.GET("/me", h.GetMe)
	rg.GET("/logout", h.Logout)
	rg.GET("/dist_logout", h.DistLogout)
	rg.POST("/idp", h.IDP)
	rg.GET("/idp/:entity_id", h.IDPAuth)
	rg.POST("/idp/:entity_id/return", h.IDPAuthReturn)
	rg.POST("/single_account/register", h.AccountRegister)
	rg.GET("/single_account/confirm_reg/:hash", h.ConfirmReg)
	rg.POST("/single_account/recovery", h.Recovery)
	rg.POST("/single_account/add_email/:hash", h.AddEmail)
	rg.GET("/confirm_email/:hash", h.ConfirmEmail)
	rg.POST("/single_account/recovery/:hash", h.RecoveryConfirm)
	rg.POST("/single_account/auth", h.Auth)
	rg.GET("/2fa/confirm/:hash", h.ConfirmMFA)
	rg.POST("/ldap", h.LDAPAuth)
	rg.GET("/:provider", h.OAuth2)
	rg.GET("/:provider/return", h.OAuthReturnGet)
	rg.POST("/:provider/return", h.OAuthReturnPost)

	h.apiValidator = jsonschema.NewAPIValidator()
	h.apiValidator.Add("account_register", SchemaAccountRegister)
	h.apiValidator.Add("account_recovery", SchemaAccountRecovery)
	h.apiValidator.Add("account_auth", SchemaAccountAuth)
	h.apiValidator.Add("account_add_email", SchemaAccountAddEmail)
	h.apiValidator.Add("account_idp", SchemaAccountIDP)

}

// OAuth2 Get provider redirection
// @Summary Get provider redirection
// @Tags Auth
// @Accept json
// @Produce json
// @Success 302
// @Router /auth/{provider} [get]
func (h *AuthHandler) OAuth2(ctx *gin.Context) {
	q := ctx.Request.URL.Query()
	providerName := ctx.Param("provider")
	redirectURI0 := ctx.Query("redirect_uri")
	if redirectURI0 == "undefined" || redirectURI0 == "" {
		q.Set("redirect_uri", h.US.Cfg.URL+"/profile")
	}
	if !h.CS.IsAvailableDomain(ctx, redirectURI0) {
		RspWithErr(ctx, http.StatusBadRequest, nil, nil, apperr.Msgs[apperr.ErrRedirectURLIsNotAllowed], apperr.ErrRedirectURLIsNotAllowed)
		return
	}
	sp, ok := h.samlProviders[providerName]
	if ok {
		stateID, err := sp.StartAuthFlow(ctx)
		if err != nil {
			return
		}
		h.US.Cache.With(cache.TypeOAuth2StateID, stateID).Set(ctx, q.Encode())
		return

	}
	stateID := rand.Seq(32)
	h.US.Cache.With(cache.TypeOAuth2StateID, stateID).Set(ctx, q.Encode())
	q.Add("provider", providerName)
	q.Add("state", stateID)
	ctx.Request.URL.RawQuery = q.Encode()
	p, ok := h.gothProviders[providerName]
	if !ok {
		panic(fmt.Errorf("provider %s not found: %w", ctx.Param("provider"), apperr.ErrNotFound))
	}
	toProviderURL, err := p.Redirect(ctx, stateID)
	if err != nil {
		panic(fmt.Errorf("provider %s redirect error %w", ctx.Param("provider"), err))
	}
	ctx.Redirect(http.StatusFound, toProviderURL) // it is OK
}

// OAuthReturnGet Callback from provider redirection
// @Summary Callback from provider redirection
// @Tags Auth
// @Accept json
// @Produce json
// @Success 302
// @Router /auth/{provider}/return [get]
func (h *AuthHandler) OAuthReturnGet(ctx *gin.Context) {
	state := ctx.Query("state_id") // workaround for auth_pb
	if state == "" {
		state = ctx.Query("state")
	}
	providerName := ctx.Param("provider")
	p, ok := h.gothProviders[providerName]
	if !ok {
		panic(fmt.Errorf("provider %s not found: %w", ctx.Param("provider"), apperr.ErrNotFound))
	}
	ui, err := p.Return(ctx, state)
	if err != nil {
		ErrorPage(ctx, "", apperr.Msgs[apperr.ErrInternalError], err)
		return
	}
	exprTime := 0
	if provideCfg, ok := h.Cfg.AuthProviders.SAML[providerName]; ok {
		exprTime = provideCfg.CookieExprTime
	}
	if h.IsIDPLogin(ctx, ui.Email) {
		ErrorPage(ctx, "", apperr.Msgs[apperr.ErrCorporateDomain], nil)
		return
	}
	h.postReturn(ctx, providerName, ui, state, exprTime)
}

// OAuthReturnPost Callback from provider redirection
// @Summary Callback from provider redirection
// @Tags Auth
// @Accept json
// @Produce json
// @Success 302
// @Router /auth/{provider}/return [post]
func (h *AuthHandler) OAuthReturnPost(ctx *gin.Context) {

	providerName := ctx.Param("provider")

	var ui providers.User
	var state string
	var err error
	if p, ok := h.gothProviders[providerName]; ok {
		body, _ := io.ReadAll(ctx.Request.Body)
		ctx.Request.Body = io.NopCloser(strings.NewReader(string(body)))
		q, err1 := url.ParseQuery(string(body))
		apperr.Error(err1)
		state = q.Get("state_id") // workaround for auth_pb
		if state == "" {
			state = q.Get("state")
		}
		ui, err = p.Return(ctx, state)

	} else {
		sp, ok := h.samlProviders[providerName]
		if !ok {
			panic(fmt.Errorf("provider %s not found: %w", ctx.Param("provider"), apperr.ErrNotFound))
		}
		ui, state, err = sp.Return(ctx)
	}
	if err != nil {
		ErrorPage(ctx, "", apperr.Msgs[apperr.ErrInternalError], err)
		return
	}
	exprTime := 0
	if provideCfg, ok := h.Cfg.AuthProviders.SAML[providerName]; ok {
		exprTime = provideCfg.CookieExprTime
	}
	if h.IsIDPLogin(ctx, ui.Email) {
		ErrorPage(ctx, "", apperr.Msgs[apperr.ErrCorporateDomain], nil)
		return
	}
	h.postReturn(ctx, providerName, ui, state, exprTime)

}

func (h *AuthHandler) bindUser(ctx *gin.Context, userID int, ui providers.User) {
	logins, err := h.US.GetLogins(ctx, []int{userID})
	apperr.Error(err)
	for t, l := range ui.Logins() {
		isExist := false
		for _, item := range logins {
			if item.Type == t && item.Login == l {
				isExist = true
				break
			}
		}
		if !isExist {
			h.US.BindLoginToUser(ctx, userID, t, l, "", "")
		}
	}
}

func (h *AuthHandler) findUser(ctx *gin.Context, ui providers.User) int {
	for t, l := range ui.Logins() {
		userInfos := h.US.GetUsersByLogin(ctx, t, l)
		if len(userInfos) > 1 {
			panic("something went wrong, more than one user with the same login")
		}
		if len(userInfos) > 0 {
			return userInfos[0].ID
		}
	}
	return 0
}

func (h *AuthHandler) postReturn(ctx *gin.Context, provider string, ui providers.User, state string, exprTime int) {
	ui.Login = strings.ToLower(ui.Login)
	ui.Email = strings.ToLower(ui.Email)
	userID := h.findUser(ctx, ui)
	if userID == 0 && ui.LDAP == "" && ui.Email == "" {
		hash := rand.Seq(32)
		cDataBin, err := json.Marshal(ui)
		apperr.Error(err)
		h.US.Cache.With(cache.TypeAddEmail, hash).Set(ctx, string(cDataBin))
		qs := url.Values{}
		qs.Add("hash", hash)

		stateQ, err := h.US.Cache.With(cache.TypeOAuth2StateID, state).Get(ctx)
		apperr.Error(err)
		q1, err := url.ParseQuery(stateQ)
		apperr.Error(err)
		if q1.Get("title") != "" {
			qs.Set("title", q1.Get("title"))
		}
		if q1.Get("photo") != "" {
			qs.Set("photo", q1.Get("photo"))
		}

		if q1.Get("redirect_uri") != "" {
			qs.Set("redirect_uri", q1.Get("redirect_uri"))
		} else {
			qs.Set("redirect_uri", h.US.Cfg.URL+"/profile")
		}
		ctx.Redirect(http.StatusFound, "/enter/add_email?"+qs.Encode())
		return
	}
	var err error
	if userID == 0 {
		userID, err = h.getOrCreateAuthProviderUser(ctx, ui)
		apperr.Error(err)
	} else {
		h.bindUser(ctx, userID, ui)
	}
	if !h.US.ValidDomain(ui.Email) {
		ErrorPage(ctx, "", "Domain is not allowed", apperr.ErrBadRequest)
		return
	}

	if ui.LoginType == authz.AuthTypeCloudAccount {
		h.US.CreateExtToken(ctx, ui.Login, ui.LoginType, ui.AccessToken)
	}

	if redirectURL := h.maybeCheck2fa(ctx, userID, state); redirectURL != "" {
		httputil.Redirect(ctx, h.CS, redirectURL)
		return
	}

	//TODO: api_user:update_user_state
	//TODO: maybe_forbidden_enter
	//TODO: api_counters:update_logins_count(LoginType),

	//TODO: find right expiration time
	if provider == "keycloak" && h.US.Cfg.Keycloak.Sync {
		h.SyncWithKeyCloak(ctx, ui.AccessToken)
	}
	if h.US.Cfg.AllowAuthorizations {
		if idp, err := h.OS.GetIDPByEntityID(ctx, provider, false); err == nil {
			if idp.Status == organization.IDPStatusAuthorization {
				slog.Info("IDP is in authorization mode", "entity_id", provider)
				adminUID, _ := h.US.AS.GetAdminInfo()
				existsWorkspace := h.SyncWithSAML(ctx, adminUID, userID, ui.RawData, idp)
				if !existsWorkspace {
					msg := "Cannot login. Please contact administrator if you should have access to the system"
					ctx.String(http.StatusForbidden, msg)
					return
				}
			}
		}

	}

	h.SetAuthorizeCookie(ctx, userID, exprTime)
	if provider == "pmi_azure" {
		pcfg := h.Cfg.AuthProviders.SAML[provider]
		wss := pcfg.PMI.Workspaces
		if len(wss) == 0 {
			wss = []string{"9f3fc410-3f35-4a21-88c5-4d84ff24dd89", "8b57f440-8eed-4289-92f6-a7e6bfefce0c"}
		}

		for _, extWID := range wss {
			wi, err := h.RS.AS.GetResourceByExtID(ctx, extWID, authz.TypeWorkspace, nil)
			apperr.Error(err)
			if h.RS.AS.MustHave(ctx, userID, wi.ID, authz.TypePermSA, authz.PermWorkspace) != nil {
				lgns, _ := h.US.GetLogins(ctx, []int{userID})
				if len(lgns) == 0 {
					panic("user logins not found")
				}
				isPMI := false
				for _, lgn := range lgns {
					// check if lgn has @pmintl.net in the end of value
					for _, d := range pcfg.PMI.Domains {
						if strings.HasSuffix(lgn.Login, "@"+d) {
							isPMI = true
							break
						}
					}
				}
				if isPMI {
					slog.Info("user has been added to PMI workspace", "uid", userID, "workspace_id", wi.ID)
					err := h.RS.AddUsers(ctx, 0, wi.ID, []role.AddUserReq{{UserID: userID, RoleID: pcfg.PMI.RoleID}})
					apperr.Error(err)
				}
			}
			ai, _ := ctx.Get("auth_info")
			h.US.WHS.Push(ctx, wi.ID, webhook.NameLogin, webhook.Login{
				ID:       userID,
				Login:    ui.Login,
				AuthInfo: ai,
			})
		}

		cfg, ok := h.Cfg.AuthProviders.SAML["pmi_azure"]
		if !ok {
			panic("pmi_azure cfg not found")
		}
		ctx.Redirect(http.StatusFound, cfg.PMI.ReturnURL) // it is OK, redirect to PMI

		return
	}
	h.Redirect(ctx, userID, state)

}

func (h *AuthHandler) Redirect(ctx *gin.Context, uid int, state string) {
	if stateQ, err := h.US.Cache.With(cache.TypeOAuth2StateID, state).Get(ctx); err == nil {
		q1, err := url.ParseQuery(stateQ)
		apperr.Error(err)
		rURL := ToRequiredPageIfSomething(ctx, h.US, uid, q1.Get("redirect_uri"), "", "")
		httputil.Redirect(ctx, h.CS, rURL)
		slog.Debug("Redirect", "redirect_uri", rURL)
		h.US.Cache.With(cache.TypeOAuth2StateID, state).Del(ctx)
		return
	}
	// move client to main page

	log.Println("decide why we are here")
	ctx.Redirect(http.StatusFound, h.US.Cfg.URL+"/profile")
}

type CustomClaims struct {
	jwt.RegisteredClaims
	Opt []string `json:"opt"`
}

func (h *AuthHandler) SetAuthorizeCookie(ctx *gin.Context, uid, sessionExprTime int) {
	ui, err := h.US.GetByID(ctx, uid)
	apperr.Error(err)
	if !ui.Status && h.US.Cfg.Type == "cloud" && ui.UpdatedStatusAt == nil {
		apperr.Error(h.US.UpdateStatus(ctx, authz.StatusUserActive, []*user.Row{&ui}))
		lid := h.LS.CreateFreeIfNeeded(ctx, uid, ptrutil.Ptr(0))
		if lid > 0 {
			h.LS.SendNotification(ctx, lid, license.AttrStateChanges, 0, 0)
		}
	}
	if uid > 0 {
		h.US.Base.ES.Push(ctx, 0, event.NameUserLogin, event.UserLogin{UserID: uid})
	}
	if sessionExprTime == 0 {
		sessionExprTime = h.US.Cfg.SettingInt("capi_cookie_expr_time", 3600*24*30)
	}
	// if token has idle option, then this token will be used for idle session ( sensitive to inactivity)
	token := authz.GenerateToken(uid, h.US.Cfg.Secret, time.Duration(h.Cfg.RefreshTokenExpireTime)*time.Second, []string{"idle"})
	h.US.UpdateLastEntranceDateAndLang(ctx, uid, "en")
	if h.Cfg.CookieSameSite != "" {
		switch h.Cfg.CookieSameSite {
		case "None":
			ctx.SetSameSite(http.SameSiteNoneMode)
		case "Lax":
			ctx.SetSameSite(http.SameSiteLaxMode)
		case "Strict":
			ctx.SetSameSite(http.SameSiteStrictMode)
		default:
			panic("invalid cookie same site")
		}
	}
	ctx.SetCookie("token", token, h.Cfg.RefreshTokenExpireTime, "/", "", true, true)
	h.US.Cache.With(cache.TypeSession, strconv.Itoa(uid), time.Duration(h.Cfg.RefreshTokenExpireTime)*time.Second).HMSet(ctx,
		"ttl", sessionExprTime, "iat", time.Now().Unix())
}

type MeLoginsRsp struct {
	Login      string  `json:"login"`
	Type       string  `json:"type"`
	CreateTime int     `json:"create_time"`
	Hash1      *string `json:"hash1"`
}

type MeRsp struct {
	RequestID  string        `json:"request_id"`
	Result     string        `json:"result"`
	UserID     int           `json:"user_id"`
	Nick       string        `json:"nick"`
	UserPhoto  string        `json:"user_photo"`
	Login      string        `json:"login"`
	Type       string        `json:"type"`
	Logins     []MeLoginsRsp `json:"logins"`
	Lang       interface{}   `json:"lang"`
	Status     string        `json:"status"`
	CreateTime int           `json:"create_time"`
	Is2Fa      bool          `json:"is_2fa"`
	Options    struct {
		DisableWorkspaceCreation bool `json:"disable_workspace_creation"`
	} `json:"options"`
}

// GetMe gets users info
// @Summary Get users info
// @Tags Auth
// @Accept json
// @Produce json
// @Security BasicAuth
// @Success 200 {object} MeRsp
// @Router /auth/me [get]
func (h *AuthHandler) GetMe(ctx *gin.Context) {
	uid, _ := ctx.Value("uid").(int)
	if uid == 0 {
		RspWithErr(ctx, http.StatusOK, nil, nil, "not authorized", apperr.ErrForbidden)
		return
	}

	ctx.JSON(http.StatusOK, me(ctx, h.OS, h.US, uid))
}

// DistLogout dist logout with redirect
// @Summary Dist logout with redirect (from clients web interfaces)
// @Tags Auth
// @Accept json
// @Produce json
// @Security BasicAuth
// @Success 302
// @Router /auth/dist_logout [get]
func (h *AuthHandler) DistLogout(ctx *gin.Context) {
	rURL := ctx.Query("redirect_uri")
	if rURL == "" {
		rURL = h.US.Cfg.URL
	}
	uid, _ := ctx.Value("uid").(int)
	if uid > 0 {
		h.US.Logout(ctx, uid)
	}
	// remote cookie token
	ctx.SetCookie("token", "", -1, "/", "", true, true)
	// TODO: add authpb logic
	httputil.Redirect(ctx, h.CS, rURL)
}

// Logout logouts user
// @Summary Logout from account web interface
// @Tags Auth
// @Accept json
// @Produce json
// @Security BasicAuth
// @Success 200
// @Router /auth/logout [get]
func (h *AuthHandler) Logout(ctx *gin.Context) {
	uid, _ := ctx.Value("uid").(int)
	if uid > 0 {
		h.US.Logout(ctx, uid)
	}
	// remote cookie token
	ctx.SetCookie("token", "", -1, "/", "", true, true)
	// TODO: add authpb logic

	ctx.JSON(http.StatusOK, gin.H{
		"result": "ok",
	})
}

type ReqRegister struct {
	Login                  string  `json:"login"`
	Password               string  `json:"password"`
	Nick                   string  `json:"nick"`
	RedirectURI            *string `json:"redirect_uri,omitempty"`
	RecaptchaResponseField *string `json:"grecaptcha_response_field,omitempty"`
	Title                  string  `json:"title"`
	Photo                  string  `json:"photo"`
}

func me(ctx *gin.Context, os *organization.Service, us *user.Service, uid int) MeRsp {
	userInfo, err := us.GetByID(ctx, uid)
	apperr.Error(err)

	rsp := MeRsp{
		RequestID: rand.Seq(16),
		Result:    "ok",
		UserID:    userInfo.ID,
		Nick:      userInfo.Name,
		Lang:      nil,
		//TODO: actived
		Status:     "actived",
		CreateTime: userInfo.CreateTime,
		//TODO: Is2Fa
		Is2Fa: false,
	}
	if userInfo.Photo != nil {
		rsp.UserPhoto = *userInfo.Photo
	}

	sLogins, err := us.GetLogins(ctx, []int{uid})
	apperr.Error(err)

	for _, l := range sLogins {
		if l.Type == authz.AuthTypeGoogle || l.Type == authz.AuthTypeSimpleEmail {
			rsp.Login = l.Login
			break
		}
	}

	logins := make([]string, len(sLogins))
	for i, item := range sLogins {
		if rsp.Type == "" && (item.Type == authz.AuthTypeGoogle || item.Type == authz.AuthTypeSimpleEmail) {
			rsp.Type = authz.ParseAuthType(item.Type)
			rsp.Login = item.Login
		}
		logins[i] = item.Login
	}
	rsp.Options.DisableWorkspaceCreation = os.IsForbiddenToCreateWS(ctx, uid, logins)

	if rsp.Type == "" && len(sLogins) > 0 {
		rsp.Type = authz.ParseAuthType(sLogins[0].Type)
		rsp.Login = sLogins[0].Login
	}
	rsp.Logins = make([]MeLoginsRsp, len(logins))
	if rsp.Type == "cloud_account" {
		rsp.Type = "google"
	}
	for i, item := range sLogins {
		if item.Type == authz.AuthTypeCloudAccount {
			item.Type = authz.AuthTypeGoogle
		}
		rsp.Logins[i] = MeLoginsRsp{
			CreateTime: item.CreateTime,
			Login:      item.Login,
			Type:       authz.ParseAuthType(item.Type),
		}
	}
	return rsp
}

func (h *AuthHandler) getOrCreateAuthProviderUser(ctx *gin.Context, ui providers.User) (int, error) {
	lang := "en"
	var uid int
	if ui.Email != "" && ui.LDAP != "" {
		//	create google account
		uid = h.US.CreateOrGetUser(ctx, authz.AuthTypeGoogle, ui.Name, ui.Photo, ui.Email, ui.AccessToken, "", lang, ui.Provider)
		//	and bind ldap to google
		h.US.BindLoginToUser(ctx, uid, authz.AuthTypeLDAP, ui.LDAP, "", "")

	}
	if ui.Email != "" && ui.LDAP == "" {
		//	create google account
		uid = h.US.CreateOrGetUser(ctx, authz.AuthTypeGoogle, ui.Name, ui.Photo, ui.Email, ui.AccessToken, "", lang, ui.Provider)

	}
	if ui.Email == "" && ui.LDAP != "" {
		//	create ldap account
		uid = h.US.CreateOrGetUser(ctx, authz.AuthTypeLDAP, ui.Name, ui.Photo, ui.LDAP, ui.AccessToken, "", lang, ui.Provider)
	}
	if ui.Email == "" && ui.LDAP == "" {
		return 0, apperr.New(apperr.ErrBadRequest, "all logins are empty")
	}

	if ui.LoginType != authz.AuthTypeGoogle {
		// need to add id instead of email because email is google type
		h.US.BindLoginToUser(ctx, uid, ui.LoginType, ui.Login, "", "")
	}

	return uid, nil

}

// ConfirmMFA confirms 2fa code
// @Summary Confirms 2fa code
// @Tags Auth
// @Accept json
// @Produce json
// #Param code query string true "2fa code"
// @Param hash path string true "hash"
// @Security BasicAuth
// @Success 200
// @Router /auth/2fa/confirm/{hash} [get]
func (h *AuthHandler) ConfirmMFA(ctx *gin.Context) {
	hash := ctx.Param("hash")
	code := ctx.Query("code")
	uid, redirectURI, err := h.MFA.CheckOTP(ctx, hash, code)
	apperr.Error(err)
	h.SetAuthorizeCookie(ctx, uid, 0)
	ctx.JSON(http.StatusOK, gin.H{
		"redirect":   redirectURI,
		"request_id": rand.Seq(16),
		"result":     "ok",
	})

}

func (h *AuthHandler) maybeCheck2fa(ctx *gin.Context, userID int, state string) string {
	sMFA, err := h.MFA.GetSettings(ctx, userID)
	switch {
	case err == nil:
		stateQ, err := h.US.Cache.With(cache.TypeOAuth2StateID, state).Get(ctx)
		apperr.Error(err)
		q1, err := url.ParseQuery(stateQ)
		apperr.Error(err)
		path := h.MFA.CreateOTP(ctx, userID, sMFA.Password, q1.Get("redirect_uri"))
		h.US.Cache.With(cache.TypeOAuth2StateID, state).Del(ctx)
		return path
	case errors.Is(err, sql.ErrNoRows):
		return ""
	default:
		panic(err)
	}
}

func (h *AuthHandler) ConfirmEmail(ctx *gin.Context) {
	// CONFIRM ADDED EMAIL (for facebook and github auth)___________________________________________________________________
	hash := ctx.Param("hash")
	addEmailInfoBin, err := h.US.Cache.With(cache.TypeAddEmailConfirm, hash).Pop(ctx)
	if err != nil {
		ErrorPage(ctx, "", "This confirmation link has already been used", nil)
		return
	}
	var addEmailInfo AddEmailInfo
	apperr.Error(json.Unmarshal([]byte(addEmailInfoBin), &addEmailInfo))
	if addEmailInfo.Email == "" {
		panic("something goes wrong")
	}

	userID, err := h.getOrCreateAuthProviderUser(ctx, addEmailInfo.User)
	apperr.Error(err)
	h.SetAuthorizeCookie(ctx, userID, 0)
	// move client to main page
	httputil.Redirect(ctx, h.CS, addEmailInfo.RedirectURL)

}

type AddEmailReq struct {
	Email       string `json:"email"`
	RedirectURL string `json:"redirect_uri"`
}

func (h *AuthHandler) SyncWithKeyCloak(ctx *gin.Context, tokenSt string) {
	// Parse the JWT.
	token, err := jwt1.Parse(tokenSt, h.JWKS.Keyfunc)
	apperr.Error(err, "Failed to parse the JWT")

	// Check if the token is valid.
	if !token.Valid {
		panic(apperr.New(apperr.ErrBadRequest, "The token is not valid."))
	}
	if claims, ok := token.Claims.(jwt1.MapClaims); ok {
		email := claims["email"].(string)
		h.US.CreateExtToken(ctx, email, authz.AuthTypeKeycloak, tokenSt)
		err := h.SyncWithPumbKeycloak(ctx, h.US.Cfg.Keycloak, claims)
		apperr.Log(err, "failed to sync user")
		ctx.Status(http.StatusOK)
		return
	}
	ctx.Status(http.StatusBadRequest)
}

func (h *AuthHandler) InitKeycloak(ctx context.Context) {
	// Create the keyfunc options. Use an error handler that logs. Refresh the JWKS when a JWT signed by an unknown KID
	// is found or at the specified interval. Rate limit these refreshes. Timeout the initial JWKS refresh request after
	// 10 seconds. This timeout is also used to create the initial context.Context for keyfunc.Get.
	options := keyfunc.Options{
		Ctx: ctx,
		RefreshErrorHandler: func(err error) {
			log.Printf("There was an error with the jwt.Keyfunc\nError: %s", err.Error())
		},
		RefreshInterval:   time.Hour,
		RefreshRateLimit:  time.Minute * 5,
		RefreshTimeout:    time.Second * 10,
		RefreshUnknownKID: true,
	}
	kURL := h.US.Cfg.Keycloak.URL
	realm := h.US.Cfg.Keycloak.Realm

	if kURL[len(kURL)-1:] != "/" {
		kURL += "/"
	}
	jwksURL := fmt.Sprintf("%sauth/realms/%s/protocol/openid-connect/certs", kURL, realm)
	// Create the JWKS from the resource at the given URL.
	jwks, err := keyfunc.Get(jwksURL, options)
	apperr.Error(err, "Failed to create JWKS from resource at the given URL")
	h.JWKS = jwks
}

func (h *AuthHandler) LDAPAuth(ctx *gin.Context) {
	body, _ := io.ReadAll(ctx.Request.Body)
	q, err := url.ParseQuery(string(body))
	apperr.Error(err)
	login := strings.TrimSpace(strings.ToLower(q.Get("login")))
	if len(login) < 3 || len(login) > 25 {
		RspWithErr(ctx, http.StatusOK, nil, nil, "invalid ldap login", apperr.ErrBadRequest)
		return
	}
	password := q.Get("password")
	if password == "" {
		RspWithErr(ctx, http.StatusOK, nil, nil, "invalid ldap password", apperr.ErrBadRequest)
		return
	}
	if h.ldapProvider == nil {
		RspWithErr(ctx, http.StatusOK, nil, nil, "ldap provider is not configured", apperr.ErrBadRequest)
		return
	}
	nick, err := h.ldapProvider.Auth(login, password)
	if err != nil {
		RspWithErr(ctx, http.StatusOK, nil, nil, apperr.GetMsg(err), err)
		return
	}
	uid := h.US.CreateOrGetUser(ctx, authz.AuthTypeLDAP, nick, "", login, "", "", "en", "ldap")
	redirectURI := ctx.Query("redirect_uri")
	if redirectURI == "" {
		redirectURI = h.US.Cfg.URL
	}
	if !h.CS.IsAvailableDomain(ctx, redirectURI) {
		RspWithErr(ctx, http.StatusBadRequest, nil, nil, apperr.Msgs[apperr.ErrRedirectURLIsNotAllowed], apperr.ErrRedirectURLIsNotAllowed)
		return
	}

	sMFA, err := h.MFA.GetSettings(ctx, uid)
	switch {
	case err == nil:
		path := h.MFA.CreateOTP(ctx, uid, sMFA.Password, redirectURI)
		ctx.JSON(http.StatusOK, gin.H{
			"redirect":   h.US.Cfg.URL + path,
			"request_id": rand.Seq(16),
			"result":     "ok",
		})
		return
	case errors.Is(err, sql.ErrNoRows):
	default:
		panic(err)
	}
	h.SetAuthorizeCookie(ctx, uid, 0)
	httputil.Redirect(ctx, h.CS, ToRequiredPageIfSomething(ctx, h.US, uid, redirectURI, "", ""))

}
