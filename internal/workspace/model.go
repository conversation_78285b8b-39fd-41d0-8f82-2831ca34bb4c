package workspace

import (
	"time"

	"github.com/lib/pq"

	"sa/internal/user"
)

type OwnerRow struct {
	ID    int     `json:"id"`
	Name  string  `json:"name"`
	Color *string `json:"color"`
	user.BaseRow
}
type RoleRow struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}
type AllRow struct {
	ID         int       `db:"id"`
	ExtID      string    `db:"ext_id"`
	Name       string    `db:"name"`
	Photo      *string   `db:"photo"`
	Color      *string   `db:"color"`
	Status     int       `db:"status"`
	CreatedAt  time.Time `db:"created_at"`
	UsersCount int       `db:"users_count"`
}
type RowShort struct {
	ID          int       `db:"id"`
	ExtID       string    `db:"ext_id"`
	Name        string    `db:"name"`
	Photo       *string   `db:"photo"`
	Color       *string   `db:"color"`
	Status      int       `db:"status"`
	UserBlocked bool      `db:"user_blocked"`
	CreatedAt   time.Time `db:"created_at"`
	Settings    Settings  `db:"settings"`
}
type Row struct {
	ID          int       `db:"id"`
	ExtID       string    `db:"ext_id"`
	Name        string    `db:"name"`
	Photo       *string   `db:"photo"`
	Color       *string   `db:"color"`
	Status      int       `db:"status"`
	UserBlocked bool      `db:"user_blocked"`
	CreatedAt   time.Time `db:"created_at"`

	UsersCount    int           `db:"users"`
	GroupsCount   int           `db:"groups"`
	RolesCount    int           `db:"roles"`
	InvitesCount  int           `db:"invites"`
	APIUsersCount int           `db:"api_users"`
	Owners        pq.Int64Array `db:"owners"`
	Settings      Settings      `db:"settings"`
}

type ShortRow struct {
	ID        int       `db:"id"`
	ExtID     string    `db:"ext_id"`
	Name      string    `db:"name"`
	Photo     *string   `db:"photo"`
	Color     *string   `db:"color"`
	Status    int       `db:"status"`
	CreatedAt time.Time `db:"created_at"`
}

type CreateRowUsers struct {
	RoleID int `json:"role_id"`
	UserID int `json:"user_id"`
}
type CreateRowAPI struct {
	UserID  int    `json:"user_id"`
	OwnerID int    `json:"owner_id"`
	URL     string `json:"url"`
}

type CreateRow struct {
	Name       string           `db:"name"`
	Photo      *string          `db:"photo"`
	Color      *string          `db:"color"`
	ExtID      *string          `db:"ext_id,omitempty"`
	Attributes struct{}         `db:"attributes"`
	Users      []CreateRowUsers `db:"users"`
	APIs       []CreateRowAPI   `db:"apis"`
}

type AISetting struct {
	Provider string `json:"provider,omitempty"`
	Model    string `json:"model,omitempty"`
	APIKey   string `json:"api_key,omitempty"`
}

type AISettings struct {
	Actor   AISetting   `json:"actor,omitempty"`
	System  AISetting   `json:"system,omitempty"`
	Console []AISetting `json:"console,omitempty"`
}

type Settings struct {
	DisableInvites    *bool      `json:"disable_invites,omitempty"`
	AllowedDomains    *[]string  `json:"allowed_domains,omitempty"`
	ForbiddenDomains  *[]string  `json:"forbidden_domains,omitempty"`
	SimClient         *string    `json:"sim_client,omitempty"`
	AutoRecording     *bool      `json:"auto_recording,omitempty"`
	TranscriptionLang *string    `json:"transcription_lang,omitempty"`
	FileTTL           *int       `json:"file_ttl,omitempty"`
	AISettings        AISettings `json:"ai_settings,omitempty"`
}

type UpdateRow struct {
	Name     string
	Photo    *string
	Color    *string
	Settings Settings
}
