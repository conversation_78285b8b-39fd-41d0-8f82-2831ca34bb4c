package workspace

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"sa/internal/group"
	"sa/internal/webhook"
	"strconv"
	"strings"
	"sync"
	"time"

	"sa/internal/event"
	"sa/pkg/runtime"

	"github.com/google/uuid"
	"github.com/pkg/errors"

	"sa/apperr"
	"sa/internal/authz"
	"sa/internal/base"
	"sa/internal/cache"
	"sa/internal/role"
	"sa/internal/user"
	"sa/pkg/pgsql"
)

type Filter struct {
	IDs       []int
	UID       int
	MemberIDs []int
	RoleID    int
	Search    string
}

type AllFilter struct {
	Search   string
	Statuses []int
}

type Service struct {
	us *user.Service
	rs *role.Service
	gs group.Service
	db Storager
	*base.Base
	wg sync.WaitGroup
}

func (s *Service) PreInit(ctx context.Context) (rsp bool) {
	rsp = false
	defer func() {
		if r := recover(); r != nil {
			slog.Error("waiting for database setup", "err", r)
			rsp = true
		}
	}()
	var adminWID, adminUID int

	adminUsers := s.us.GetUsersByLogin(ctx, authz.Types[s.Cfg.AdminUser.Type], s.Cfg.AdminUser.Login)
	if len(adminUsers) > 0 {
		adminUID = adminUsers[0].ID
	}

	s.Cache.With(cache.TypeWorkspaceExtID, s.Base.Cfg.AdminWorkspace).Del(ctx)
	adminWID, _ = s.GetByExtID(ctx, s.Base.Cfg.AdminWorkspace)
	s.Base.AS.SetAdminInfo(adminUID, adminWID)
	if adminWID == 0 || adminUID == 0 {
		rsp = true
		return rsp
	}
	// TODO: remove after 3.9 version release
	s.addEnvRules(ctx, adminWID, authz.OptionIgnoreExist)
	chain := make([]authz.ChainElement, 0)
	for _, perm := range authz.PermsInRoles[authz.TypePermSA][authz.RoleSupport] {
		p := perm
		chain = append(chain, authz.ChainElement{
			Options:   authz.OptionIgnoreExist,
			Operation: authz.OperationInsert,
			Data: authz.Rule{
				WID:   &adminWID,
				SType: authz.TypeRole,
				SID:   authz.RoleSupport,
				OType: authz.TypePermSA,
				OID:   &p,
				Rel:   authz.RelInclude,
			},
		})
	}
	apperr.Error(s.Base.AS.Apply(ctx, chain))
	//.
	if s.AS.MustHave(ctx, adminUID, adminWID, authz.TypePermEnv, authz.PermEnvSettingsManagement) != nil {
		_ = s.rs.AddUsers(ctx, 0, adminWID, []role.AddUserReq{
			{
				UserID: adminUID,
				RoleID: authz.RoleAdmin,
			},
		})
	}
	return rsp
}
func (s *Service) Init(ctx context.Context) {
	var adminWID, adminUID int
	adminUsers := s.us.GetUsersByLogin(ctx, authz.Types[s.Cfg.AdminUser.Type], s.Cfg.AdminUser.Login)
	if len(adminUsers) == 0 {
		adminUID = s.us.CreateOrGetUser(ctx, authz.Types[s.Cfg.AdminUser.Type], s.Cfg.AdminUser.Login,
			"", s.Cfg.AdminUser.Login, s.Cfg.AdminUser.Password, "", "en", "system")
	} else {
		adminUID = adminUsers[0].ID
	}
	var err error
	adminWID, err = s.GetByExtID(ctx, s.Base.Cfg.AdminWorkspace)
	if err != nil {
		wreq := CreateRow{Name: "Null workspace", ExtID: &s.Base.Cfg.AdminWorkspace, Users: []CreateRowUsers{
			{
				UserID: adminUID,
				RoleID: authz.RoleOwner,
			},
		}}
		adminWID, _, err = s.Create(ctx, wreq)
		apperr.Error(err)
		s.addEnvRules(ctx, adminWID, 0)
	}
	if adminUID == 0 || adminWID == 0 {
		panic("adminUID, adminWID should not be 0")
	}
	s.AS.SetAdminInfo(adminUID, adminWID)
	if s.AS.MustHave(ctx, adminUID, adminWID, authz.TypePermEnv, authz.PermEnvSettingsManagement) != nil {
		_ = s.rs.AddUsers(ctx, 0, adminWID, []role.AddUserReq{
			{
				UserID: adminUID,
				RoleID: authz.RoleAdmin,
			},
		})
	}
}

func NewService(gs group.Service, us *user.Service, rs *role.Service, bs *base.Base, db Storager) *Service {
	rsp := &Service{
		gs:   gs,
		us:   us,
		rs:   rs,
		db:   db,
		Base: bs,
	}
	return rsp
}

func (s *Service) List(ctx context.Context, f Filter, limit, offset int, sortBy, orderBy string) (rsp []*RowShort, count int) {
	wss, count := s.db.List(ctx, f, limit, offset, sortBy, orderBy)
	if len(wss) == 0 && f.Search == "" && offset == 0 {
		name := "My workspace"
		ls, err := s.us.GetLogins(ctx, []int{f.UID})
		apperr.Error(err)
		for _, l := range ls {
			if l.Type == authz.AuthTypeGoogle || l.Type == authz.AuthTypeSimpleEmail || l.Type == authz.AuthTypeCloudAccount {
				name = l.Login
				break
			}
		}
		wreq := CreateRow{Name: name, Users: []CreateRowUsers{{
			UserID: f.UID,
			RoleID: authz.RoleOwner,
		}}}
		_, _, err = s.Create(ctx, wreq)
		apperr.Error(err)
		wss, count = s.db.List(ctx, f, limit, offset, sortBy, orderBy)
	}
	return wss, count
}

func (s *Service) Get(ctx context.Context, id, uid int) (*Row, error) {
	return s.db.Get(ctx, id, uid)
}

func (s *Service) ListAll(ctx context.Context, f AllFilter, limit, offset int, sortBy, orderBy string) (rsp []*AllRow, fullCount int) {
	return s.db.ListAll(ctx, f, limit, offset, sortBy, orderBy)
}

func (s *Service) Search(ctx context.Context, uid int, searchText string, limit int) []ShortRow {
	return s.db.Search(ctx, uid, pgsql.ToTSV(searchText), limit)
}

func (s *Service) ListByOwner(ctx context.Context, uid int) []ShortRow {
	return s.db.ListByOwner(ctx, uid)
}

func (s *Service) ListOwners(ctx context.Context, ids []int) map[int][]OwnerRow {
	return s.db.ListOwners(ctx, ids)
}

func (s *Service) GetByExtID(ctx context.Context, extID string) (int, error) {
	rsp, err := s.Cache.With(cache.TypeWorkspaceExtID, extID).GetWithUpdate(ctx, func() (interface{}, error) {
		return s.db.GetByExtID(ctx, extID)
	}, func(v []byte) (any, error) {
		return strconv.Atoi(string(v))
	})
	if err != nil {
		return 0, err
	}
	return rsp.(int), nil
}

func (s *Service) Update(ctx context.Context, uid, id int, r UpdateRow) error {
	if err := s.Base.AS.MustHave(ctx, uid, id, authz.TypePermSA, authz.PermWorkspaceManaging); err != nil {
		return err
	}
	s.db.Update(ctx, id, r)
	// TODO: to simplify this code
	roles, err := s.rs.ListUsers(ctx, 0, id, authz.RoleOwner, []int{}, "", 0, 0, "", "")
	if err != nil {
		return fmt.Errorf("failed to get workspace owners: %w", err)
	}
	ownerIDs := make([]int, len(roles))
	for i, r := range roles {
		ownerIDs[i] = r.ID
	}
	var aiSettings []event.AISetting
	// Convert AISettings object to array for event
	if r.Settings.AISettings.Actor.Provider != "" || r.Settings.AISettings.Actor.APIKey != "" {
		aiSettings = append(aiSettings, event.AISetting{
			Provider: r.Settings.AISettings.Actor.Provider,
			APIKey:   r.Settings.AISettings.Actor.APIKey,
		})
	}
	if r.Settings.AISettings.System.Provider != "" || r.Settings.AISettings.System.APIKey != "" {
		aiSettings = append(aiSettings, event.AISetting{
			Provider: r.Settings.AISettings.System.Provider,
			APIKey:   r.Settings.AISettings.System.APIKey,
		})
	}
	for _, console := range r.Settings.AISettings.Console {
		if console.Provider != "" || console.APIKey != "" {
			aiSettings = append(aiSettings, event.AISetting{
				Provider: console.Provider,
				APIKey:   console.APIKey,
			})
		}
	}
	s.Base.ES.Push(ctx, id, event.NameWsSet, event.WSSet{
		Name:  r.Name,
		Photo: r.Photo,
		Color: r.Color,
		Settings: event.Settings{
			DisableInvites:    r.Settings.DisableInvites,
			AllowedDomains:    r.Settings.AllowedDomains,
			ForbiddenDomains:  r.Settings.ForbiddenDomains,
			SimClient:         r.Settings.SimClient,
			AutoRecording:     r.Settings.AutoRecording,
			TranscriptionLang: r.Settings.TranscriptionLang,
			FileTTL:           r.Settings.FileTTL,
			AISettings:        aiSettings,
		},
		OwnerIDs: ownerIDs,
	})
	err = s.AS.StoreAvatar(ctx, r.Photo, authz.TypeWorkspace, id, true)
	if err != nil {
		return fmt.Errorf("AS.StoreAvatar: %w", err)
	}
	return nil
}

func (s *Service) Block(ctx context.Context, uid, id int) error {
	_, adminWID := s.Base.AS.GetAdminInfo()
	if adminWID == id {
		return apperr.ErrForbidden
	}
	if err := s.Base.AS.MustHave(ctx, uid, id, authz.TypePermSA, authz.PermWorkspaceManaging); err != nil {
		return err
	}
	s.db.UpdateStatus(ctx, id, StatusBlock)
	pgsql.OnCommit(ctx, func(ctx context.Context) error {
		s.Cache.With(cache.TypeWorkspaceUsers, strconv.Itoa(id)).Del(ctx)
		return nil
	})

	return nil
}

func (s *Service) UnBlock(ctx context.Context, uid, id int) error {
	if err := s.Base.AS.MustHave(ctx, uid, id, authz.TypePermSA, authz.PermWorkspaceManaging); err != nil {
		return err
	}
	s.db.UpdateStatus(ctx, id, StatusActive)
	return nil
}

func (s *Service) Delete(ctx context.Context, uid, id int) error {
	_, adminWID := s.Base.AS.GetAdminInfo()
	if adminWID == id {
		return apperr.ErrForbidden
	}
	if uid > 0 {
		if err := s.Base.AS.MustHave(ctx, uid, id, authz.TypePermSA, authz.PermWorkspaceManaging); err != nil {
			return err
		}
	}
	usrs := s.rs.Users(ctx, id)
	wi, err := s.AS.GetResource(ctx, id, authz.TypeWorkspace)
	if err != nil {
		return fmt.Errorf("GetResource: %w", err)
	}

	s.Base.ES.Push(ctx, id, event.NameWsDel, event.WSDel{AuthorID: uid})
	s.db.UpdateStatus(ctx, id, StatusDeleted)
	pgsql.OnCommit(ctx, func(ctx context.Context) error {
		for _, u := range usrs {
			s.Cache.With(cache.TypeAPIUser, strconv.Itoa(u)).Del(ctx)
		}
		s.Cache.With(cache.TypeWorkspaceUsers, strconv.Itoa(id)).Del(ctx)
		s.Cache.With(cache.TypeWorkspaceExtID, *wi.ExtID).Del(ctx)
		return nil
	})

	return nil
}

func (s *Service) Create(ctx context.Context, r CreateRow) (wid int, extID string, err error) {
	// TODO: check users list
	if r.ExtID != nil {
		extID = *r.ExtID
	} else {
		extID = uuid.New().String()
	}
	wid = s.Base.AS.AllocateIDs(ctx, "workspace_seq", 1)[0]
	chain := make([]authz.ChainElement, 0)

	workspace := authz.Resource{
		ID:         wid,
		ExtID:      &extID,
		Type:       authz.TypeWorkspace,
		Name:       r.Name,
		Photo:      r.Photo,
		Color:      r.Color,
		Attributes: "{}",
	}

	chain = append(chain, authz.ChainElement{Operation: authz.OperationInsert, Data: workspace})
	rules := make([]authz.Rule, 0)
	ownerID := 0
	users1 := make([]int, len(r.Users))
	for i, item := range r.Users {
		if !s.AS.Exists(ctx, item.UserID, authz.TypeUser) {
			return 0, "", fmt.Errorf("user %d does not exist", item.UserID)
		}
		users1[i] = item.UserID
		oid := item.RoleID
		if oid == authz.RoleOwner {
			ownerID = item.UserID
		}
		rules = append(rules, authz.Rule{
			WID:   &wid,
			SType: authz.TypeUser,
			SID:   item.UserID,
			OType: authz.TypeRole,
			OID:   &oid,
			Rel:   authz.RelMember,
		})
	}

	apis := make([]int, len(r.APIs))
	for i, item := range r.APIs {
		keyData := struct {
			Name   string  `db:"name"`
			Data   *string `db:"data"`
			Secret string  `db:"access_token"`
			Scopes string  `db:"scope"`
			Photo  *string `db:"-"`
			Color  *string `db:"-"`
		}{}
		conn := pgsql.FromContext(ctx)
		sql := `select u.name, u.data, t.access_token , t.scope
                from users u inner join  oauth2_tokens t on (t.user_id = u.id and t.type=1)
               where u.id=$1`
		err := conn.GetContext(ctx, &keyData, sql, item.UserID)
		apperr.Error(err, "wrong api_user_id")
		data := make(map[string]any)
		if keyData.Data != nil {
			err := json.Unmarshal([]byte(*keyData.Data), &data)
			apperr.Error(err)
			if photo, ok := data["photo"].(string); ok {
				keyData.Photo = &photo
			}
		}
		apis[i] = item.UserID
		scopes := make([]user.Scope, 0)
		scopesSt := strings.Split(keyData.Scopes, ",")
		addedScopes := make(map[string]struct{})
		for _, scopeSt := range scopesSt {
			scopeList := strings.Split(scopeSt, ":")
			if _, ok := addedScopes[scopeList[1]]; ok {
				continue
			}
			scopes = append(scopes, user.Scope{
				ID:   scopeList[1],
				Type: authz.ResourceNames[authz.TypeScopeControl],
			})
		}
		sReq := user.CreateUpdateAPIRow{
			Name:   keyData.Name,
			Photo:  keyData.Photo,
			Color:  keyData.Color,
			URL:    item.URL,
			Scopes: scopes,
			Secret: keyData.Secret,
		}
		_, err = s.us.CreateAPIUserNoValidate(ctx, item.OwnerID, wid, item.UserID, sReq)
		apperr.Error(err)
	}

	if ownerID == 0 {
		return wid, extID, errors.Errorf("there is no owner")
	}
	rules = append(rules, s.Base.AS.GetRoleRules(ctx, wid, authz.RoleOwner)...)
	rules = append(rules, s.Base.AS.GetRoleRules(ctx, wid, authz.RoleAdmin)...)
	rules = append(rules, s.Base.AS.GetRoleRules(ctx, wid, authz.RoleMember)...)
	rules = append(rules, s.Base.AS.GetRoleRules(ctx, wid, authz.RoleGuest)...)
	for _, item := range rules {
		chain = append(chain, authz.ChainElement{Operation: authz.OperationInsert, Data: item})
	}

	err = s.Base.AS.Apply(ctx, chain)
	apperr.Error(err)
	ownerIDs := make([]int, 0)
	for _, item := range r.Users {
		if item.RoleID == authz.RoleOwner {
			ownerIDs = append(ownerIDs, item.UserID)
		}
	}
	s.Base.ES.Push(ctx, wid, event.NameWsAdd, event.WSAdd{
		Name:     r.Name,
		Photo:    r.Photo,
		Color:    r.Color,
		OwnerIDs: ownerIDs,
	})
	err = s.AS.StoreAvatar(ctx, r.Photo, authz.TypeWorkspace, wid, true)
	if err != nil {
		return wid, extID, fmt.Errorf("AS.StoreAvatar: %w", err)
	}
	return wid, extID, nil
}

func (s *Service) Sync(ctx context.Context, clientID int) error {
	exists, _ := s.Cache.With(cache.TypeInstance, "sync").Exists(ctx)
	if exists {
		return apperr.ErrInProgress
	}

	ws := s.db.ListWithOwners(ctx)
	if len(ws) == 0 {
		return nil
	}

	s.Cache.With(cache.TypeInstance, "sync", time.Hour).Set(ctx, "1")
	s.wg.Add(len(ws))
	go func() {
		s.wg.Wait()
		s.Cache.With(cache.TypeInstance, "sync").Del(ctx)
	}()

	var sem = make(chan struct{}, 20)
	for i := range ws {
		sem <- struct{}{}
		workspace := ws[i]
		runtime.Go(func() {
			defer s.wg.Done()
			s.Base.ES.PushWithClient(ctx, workspace.ID, event.NameWsSync, event.WSAdd{
				Name:     workspace.Name,
				Photo:    workspace.Photo,
				Color:    workspace.Color,
				OwnerIDs: pgsql.Int64Array(workspace.Owners).IntArray(),
			}, clientID)
			<-sem
		})
	}
	log.Printf("sync with client ID: %d has started", clientID)
	return nil
}

func (s *Service) GetByOwner(ctx context.Context, uid int) []string {
	return s.db.GetByOwner(ctx, uid)
}

func (s *Service) addEnvRules(ctx context.Context, adminWID int, options authz.Option) {
	chain := make([]authz.ChainElement, 0)
	for r, listPerms := range authz.PermsInRoles[authz.TypePermEnv] {
		for _, perm := range listPerms {
			p := perm
			chain = append(chain, authz.ChainElement{
				Options:   options,
				Operation: authz.OperationInsert,
				Data: authz.Rule{
					WID:   &adminWID,
					SType: authz.TypeRole,
					SID:   r,
					OType: authz.TypePermEnv,
					OID:   &p,
					Rel:   authz.RelInclude,
				},
			})
		}
	}
	for r, listPerms := range authz.PermsInRoles[authz.TypePermCloudEnv] {
		for _, perm := range listPerms {
			p := perm
			chain = append(chain, authz.ChainElement{
				Options:   options,
				Operation: authz.OperationInsert,
				Data: authz.Rule{
					WID:   &adminWID,
					SType: authz.TypeRole,
					SID:   r,
					OType: authz.TypePermCloudEnv,
					OID:   &p,
					Rel:   authz.RelInclude,
				},
			})
		}
	}
	apperr.Error(s.Base.AS.Apply(ctx, chain))
}

func (s *Service) ListRoles(ctx context.Context, ids []int, uid int) map[int][]RoleRow {
	return s.db.ListRoles(ctx, ids, uid)
}

func (s *Service) Leave(ctx context.Context, uid, wid, leavingUserID, toUserID int, objOwnerIDs []int) error {

	// check perms
	if uid > 0 && uid != leavingUserID {
		if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermUserManaging); err != nil {
			return fmt.Errorf("MustHaveOld: %w", err)
		}
	}
	leavingUser, err := s.us.GetByID(ctx, leavingUserID)
	if err != nil {
		return fmt.Errorf("GetByID: %w", err)
	}

	owners := s.rs.ListOwner(ctx, wid)
	leavingIsOwner := false
	for _, item := range owners {
		if item == leavingUserID {
			leavingIsOwner = true
			break
		}
	}
	if leavingIsOwner && toUserID == 0 {
		return apperr.New(apperr.ErrNoPermissions, "owner can't leave workspace without transferring ownership")
	}
	if uid == leavingUserID && toUserID == 0 {
		toUserID = owners[0]
	}

	// update groups owner
	err = s.gs.UpdateAllOwner(ctx, wid, leavingUserID, toUserID)
	if err != nil {
		return fmt.Errorf("UpdateAllOwner: %w", err)
	}

	// update apis owner
	s.us.UpdateAPIOwner(ctx, wid, leavingUserID, toUserID)

	roles := s.rs.ListByUser(ctx, leavingUserID)
	updateUsers := make([]role.UpdateUser, 0)
	for _, item := range roles {
		if item.WID == nil || *item.WID != wid {
			continue
		}
		updateUsers = append(updateUsers, role.UpdateUser{
			RoleID: item.ID,
			UserID: leavingUserID,
			Active: false,
		})
	}

	// transfer ownership
	if leavingIsOwner {
		users := make([]int, 0)
		for _, u := range s.rs.Users(ctx, wid) {
			if u == leavingUserID {
				continue
			}
			users = append(users, u)
		}
		if s.License.CanBeExceeded(ctx, toUserID, users, false) {
			panic(apperr.ErrLicenseNewOwnerHasReached)
		}
		toUserIDRoles := s.rs.ListByUser(ctx, toUserID)
		for _, item := range toUserIDRoles {
			if item.WID == nil || *item.WID != wid {
				continue
			}
			updateUsers = append(updateUsers, role.UpdateUser{
				RoleID: item.ID,
				UserID: toUserID,
				Active: false,
			})
		}
		updateUsers = append(updateUsers, role.UpdateUser{
			RoleID: authz.RoleOwner,
			UserID: toUserID,
			Active: true,
		})
		wi, err := s.Base.AS.GetResource(ctx, wid, authz.TypeWorkspace)
		if err != nil {
			return fmt.Errorf("GetResource: %w", err)
		}
		settings := event.Settings{}
		err = json.Unmarshal([]byte(wi.Attributes), &settings)
		apperr.Error(err)
		s.Base.ES.Push(ctx, wid, event.NameWsSet, event.WSSet{
			Name:     wi.Name,
			Photo:    wi.Photo,
			Color:    wi.Color,
			OwnerIDs: []int{toUserID},
			Settings: settings,
		})
	}
	if len(updateUsers) == 0 {
		return apperr.New(apperr.ErrUserNotFound)
	}

	err = s.rs.UpdateUsersTx(ctx, uid, wid, true, updateUsers)
	if err != nil {
		return fmt.Errorf("updateUsersTx: %w", err)
	}
	roleRule := authz.Rule{
		WID:   &wid,
		SType: authz.TypeUser,
		SID:   leavingUserID,
	}
	chain := []authz.ChainElement{{Operation: authz.OperationDelete, Data: roleRule}}
	err = s.Base.AS.Apply(ctx, chain)
	if err != nil {
		return err
	}
	var uid1 *int
	if uid > 0 {
		uid1 = &uid
	}

	for _, objOwnerID := range objOwnerIDs {
		err := s.AS.MustHave(ctx, objOwnerID, wid, authz.TypePermSA, authz.PermWorkspace)
		if err != nil {
			return apperr.ErrUserNotFound
		}
	}

	usersMap := make([]event.WSUserSetItemObjOwnerID, len(objOwnerIDs))

	for i, item := range objOwnerIDs {
		usersMap[i] = event.WSUserSetItemObjOwnerID{ID: item}
	}

	s.Base.ES.Push(ctx, wid, event.NameWsUserSet, event.WSUserSet{
		Users: []event.WSUserSetItem{{
			ID:          leavingUserID,
			Name:        leavingUser.Name,
			Active:      false,
			Status:      "deleted",
			AuthorID:    uid1,
			ObjOwnerIDs: usersMap,
			Perms:       make([]event.WSUserPermItem, 0),
			Photo:       leavingUser.Photo,
			Color:       leavingUser.Color,
		}},
	})

	s.WHS.Push(ctx, wid, webhook.NameUser, webhook.User{
		Users: []webhook.UserItem{{
			ID:     leavingUserID,
			Status: "active",
			Action: "deleted",
		},
		},
	})
	s.rs.DeleteOwnerRequestsByWSUser(ctx, wid, leavingUserID)
	pgsql.OnCommit(ctx, func(ctx context.Context) error {
		s.Cache.With(cache.TypeWorkspaceUsers, strconv.Itoa(wid)).HDel(ctx, leavingUserID)
		return nil
	})
	return nil
}
